# Tbaw 项目代码文档

## 项目概述

**Tbaw** 是一个基于 Electron + React + TypeScript 的桌面应用程序，采用现代化的架构设计，具有完整的数据库管理、IPC 通信和类型安全保障。

### 技术栈

- **前端框架**: React 19.1.0 + TypeScript 5.8.3
- **桌面框架**: Electron 35.1.5
- **构建工具**: Electron-Vite 3.1.0 + Vite 6.2.6
- **数据库**: Better-SQLite3 11.10.0
- **UI 组件**: Radix UI + Tailwind CSS 4.1.8
- **状态管理**: Zustand 5.0.5
- **类型验证**: Zod 3.25.51
- **图标库**: Lucide React 0.511.0

### 项目结构

```
tbaw/
├── src/
│   ├── main/           # 主进程代码
│   ├── preload/        # 预加载脚本
│   ├── renderer/       # 渲染进程代码
│   └── shared/         # 共享类型定义
├── build/              # 构建资源
├── resources/          # 应用资源
└── out/                # 编译输出
```

## 核心架构设计

### 1. 类型系统 (`src/shared/types.ts`)

采用 **Zod Schema** 进行统一类型定义，确保编译时和运行时的类型安全。

#### 核心类型定义

```typescript
// 标签页类型
export const TabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.boolean().default(false).describe('是否为当前活跃标签页'),
  isDirty: z.boolean().default(false).describe('是否有未保存的更改'),
  isPinned: z.boolean().default(false).describe('是否为固定标签页'),
  icon: z.string().optional().describe('标签页图标路径或名称'),
  closable: z.boolean().default(true).describe('是否可关闭'),
  tooltip: z.string().optional().describe('鼠标悬停提示文本'),
  lastModified: z.date().optional().describe('最后修改时间'),
  metadata: z.record(z.any()).optional().describe('扩展元数据'),
  path: z.string().optional().describe('文件路径 (如果是文件标签页)'),
  type: TabTypeSchema.default('file').describe('标签页类型'),
  sortOrder: z.number().default(0).describe('标签页排序顺序')
})

// 窗口状态类型
export const WindowStateSchema = z.object({
  id: z.number().default(1).describe('窗口ID'),
  isMaximized: z.boolean().default(false).describe('是否最大化'),
  isMinimized: z.boolean().default(false).describe('是否最小化'),
  isFullscreen: z.boolean().default(false).describe('是否全屏'),
  isFocused: z.boolean().default(true).describe('是否获得焦点'),
  bounds: BoundsSchema.optional().describe('窗口边界信息'),
  updatedAt: z.string().optional().describe('最后更新时间')
})

// 应用设置类型
export const AppSettingsSchema = z.object({
  saveAppState: z.boolean().default(true).describe('是否保存应用状态'),
  restoreWindowState: z.boolean().default(true).describe('是否恢复窗口状态'),
  restoreTabs: z.boolean().default(true).describe('是否恢复标签页'),
  maxRecentTabs: z.number().min(1).max(50).default(10).describe('最大最近标签页数量'),
  theme: z.enum(['light', 'dark', 'system']).default('system').describe('主题设置'),
  language: z.string().default('zh-CN').describe('语言设置')
})
```

#### 类型推断

```typescript
export type Tab = z.infer<typeof TabSchema>
export type WindowState = z.infer<typeof WindowStateSchema>
export type AppSettings = z.infer<typeof AppSettingsSchema>
export type TitlebarConfig = z.infer<typeof TitlebarConfigSchema>
export type RecentFile = z.infer<typeof RecentFileSchema>
export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & { data?: T }
```

### 2. 数据库架构 (`src/main/database/`)

采用 **模块化设计**，每个功能模块独立管理，支持事务、迁移和优化。

#### 核心管理器 (`core/database-manager.ts`)

```typescript
class CoreDatabaseManager {
  private static instance: CoreDatabaseManager
  private db: Database.Database | null = null
  private isInitialized = false

  async initialize(): Promise<void> {
    // 创建数据库连接
    // 启用外键约束、WAL模式、设置缓存
    // 运行数据库迁移
  }

  getDatabase(): Database.Database {
    // 返回数据库实例
  }
}
```

#### 基础仓库类 (`core/base-repository.ts`)

提供通用的数据库访问功能：

```typescript
export abstract class BaseRepository {
  protected get db(): Database.Database
  protected transaction<T>(fn: () => T): T
  protected batchInsert<T>(tableName: string, data: T[]): void
  protected deleteOldRecords(tableName: string, timeColumn: string, daysToKeep: number): number
}
```

#### 数据库表结构

**1. 标签页表 (tabs)**

```sql
CREATE TABLE tabs (
  id TEXT PRIMARY KEY,                    -- 标签页唯一标识符
  title TEXT NOT NULL,                    -- 标签页显示标题
  active INTEGER NOT NULL DEFAULT 0,      -- 是否为当前活跃标签页 (0=否, 1=是)
  isDirty INTEGER NOT NULL DEFAULT 0,     -- 是否有未保存的更改 (0=否, 1=是)
  isPinned INTEGER NOT NULL DEFAULT 0,    -- 是否为固定标签页 (0=否, 1=是)
  icon TEXT,                              -- 标签页图标路径或名称
  closable INTEGER NOT NULL DEFAULT 1,    -- 是否可关闭 (0=不可关闭, 1=可关闭)
  tooltip TEXT,                           -- 鼠标悬停提示文本
  lastModified TEXT,                      -- 最后修改时间
  metadata TEXT,                          -- 扩展元数据 (JSON格式)
  path TEXT,                              -- 文件路径 (如果是文件标签页)
  type TEXT NOT NULL DEFAULT 'file',      -- 标签页类型
  sort_order INTEGER DEFAULT 0,           -- 标签页排序顺序
  createdAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP   -- 最后更新时间
)
```

**2. 窗口状态表 (window_state)**

```sql
CREATE TABLE window_state (
  id INTEGER PRIMARY KEY DEFAULT 1,       -- 窗口ID (通常为1，单窗口应用)
  isMaximized INTEGER NOT NULL DEFAULT 0, -- 是否最大化 (0=否, 1=是)
  isMinimized INTEGER NOT NULL DEFAULT 0, -- 是否最小化 (0=否, 1=是)
  isFullscreen INTEGER NOT NULL DEFAULT 0,-- 是否全屏 (0=否, 1=是)
  isFocused INTEGER NOT NULL DEFAULT 1,   -- 是否获得焦点 (0=否, 1=是)
  bounds TEXT,                            -- 窗口边界信息 (JSON格式)
  updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP   -- 最后更新时间
)
```

**3. 应用设置表 (settings)**

```sql
CREATE TABLE settings (
  key TEXT PRIMARY KEY,                    -- 设置项键名
  value TEXT NOT NULL,                     -- 设置项值 (JSON格式)
  updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP   -- 最后更新时间
)
```

**4. 最近访问文件表 (recent_files)**

```sql
CREATE TABLE recent_files (
  id INTEGER PRIMARY KEY AUTOINCREMENT,   -- 自增主键
  path TEXT NOT NULL UNIQUE,              -- 文件完整路径 (唯一)
  name TEXT NOT NULL,                     -- 文件名
  lastAccessed TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 最后访问时间
  fileSize INTEGER,                       -- 文件大小 (字节)
  fileType TEXT,                          -- 文件类型/扩展名
  metadata TEXT                           -- 文件元数据 (JSON格式)
)
```

#### 模块化设计

**标签页模块 (`modules/tabs/`)**

- `repository.ts`: 数据访问层，处理 SQL 操作和数据转换
- `service.ts`: 业务逻辑层，提供高级操作接口
- `index.ts`: 模块导出，提供便捷方法

**设置模块 (`modules/settings/`)**

- 管理应用配置和用户偏好
- 支持批量操作和默认值初始化

**窗口模块 (`modules/window/`)**

- 管理窗口状态持久化
- 支持窗口边界、最大化状态等

### 3. IPC 通信架构 (`src/main/ipc/`)

#### 数据库 IPC (`ipc/database.ts`)

提供前后端数据交互接口：

```typescript
export function setupDatabaseIPC(): void {
  // 标签页操作
  ipcMain.handle('db:save-tabs', async (_, tabsData: Tab[]) => { ... })
  ipcMain.handle('db:get-tabs', async () => { ... })

  // 设置操作
  ipcMain.handle('db:save-setting', async (_, key: string, value: any) => { ... })
  ipcMain.handle('db:get-setting', async (_, key: string, defaultValue?: any) => { ... })

  // 窗口状态操作
  ipcMain.handle('db:save-window-state', async (_, state: WindowState) => { ... })
  ipcMain.handle('db:get-window-state', async () => { ... })

  // 数据库维护
  ipcMain.handle('db:cleanup', async () => { ... })
}
```

#### 窗口控制 IPC (`ipc/window-controls.ts`)

提供窗口操作接口：

```typescript
export function setupWindowControlsIPC(mainWindow: BrowserWindow): void {
  ipcMain.handle('window:minimize', async () => { ... })
  ipcMain.handle('window:maximize', async () => { ... })
  ipcMain.handle('window:close', async () => { ... })
  ipcMain.handle('window:get-state', async () => { ... })
}
```

### 4. 预加载脚本 (`src/preload/index.ts`)

安全地暴露 API 给渲染进程：

```typescript
const api = {
  // 数据库操作
  database: {
    saveTabs: (tabs: Tab[]): Promise<ApiResponse> => ipcRenderer.invoke('db:save-tabs', tabs),
    getTabs: (): Promise<ApiResponse<Tab[]>> => ipcRenderer.invoke('db:get-tabs'),
    saveSetting: (key: string, value: any): Promise<ApiResponse> =>
      ipcRenderer.invoke('db:save-setting', key, value),
    getSetting: (key: string, defaultValue?: any): Promise<ApiResponse<any>> =>
      ipcRenderer.invoke('db:get-setting', key, defaultValue),
    saveWindowState: (state: WindowState): Promise<ApiResponse> =>
      ipcRenderer.invoke('db:save-window-state', state),
    getWindowState: (): Promise<ApiResponse<WindowState | null>> =>
      ipcRenderer.invoke('db:get-window-state'),
    cleanup: (): Promise<ApiResponse> => ipcRenderer.invoke('db:cleanup')
  },

  // 窗口控制
  window: {
    minimize: (): Promise<ApiResponse> => ipcRenderer.invoke('window:minimize'),
    maximize: (): Promise<ApiResponse> => ipcRenderer.invoke('window:maximize'),
    close: (): Promise<ApiResponse> => ipcRenderer.invoke('window:close'),
    getState: (): Promise<ApiResponse<any>> => ipcRenderer.invoke('window:get-state')
  },

  // 系统信息
  system: {
    getPlatform: () => process.platform,
    getVersions: () => process.versions
  }
}
```

## 详细模块分析

### 1. 标签页模块 (Tabs Module)

#### 字段设计说明

| 字段名         | 类型                 | 说明                                       | 默认值    | 约束            |
| -------------- | -------------------- | ------------------------------------------ | --------- | --------------- |
| `id`           | string               | 标签页唯一标识符，用于区分不同标签页       | -         | 必填，最小长度1 |
| `title`        | string               | 标签页显示标题，用户可见的标签名称         | -         | 必填，最小长度1 |
| `active`       | boolean              | 是否为当前活跃标签页，同时只能有一个活跃   | false     | -               |
| `isDirty`      | boolean              | 是否有未保存的更改，用于提示用户保存       | false     | -               |
| `isPinned`     | boolean              | 是否为固定标签页，固定标签页不会被自动关闭 | false     | -               |
| `icon`         | string?              | 标签页图标路径或名称，可选显示             | undefined | 可选            |
| `closable`     | boolean              | 是否可关闭，某些系统标签页不允许关闭       | true      | -               |
| `tooltip`      | string?              | 鼠标悬停提示文本，提供额外信息             | undefined | 可选            |
| `lastModified` | Date?                | 最后修改时间，用于排序和显示               | undefined | 可选            |
| `metadata`     | Record<string, any>? | 扩展元数据，存储自定义信息                 | undefined | 可选            |
| `path`         | string?              | 文件路径，如果是文件标签页则记录文件位置   | undefined | 可选            |
| `type`         | TabType              | 标签页类型，决定标签页的行为和显示         | 'file'    | 枚举值          |
| `sortOrder`    | number               | 标签页排序顺序，用于控制显示顺序           | 0         | -               |

#### 标签页类型枚举

```typescript
type TabType = 'home' | 'folder' | 'file' | 'welcome' | 'settings'
```

- `home`: 首页标签页，通常固定且不可关闭
- `folder`: 文件夹标签页，显示目录内容
- `file`: 文件标签页，显示具体文件内容
- `welcome`: 欢迎页标签页，首次启动时显示
- `settings`: 设置页标签页，应用配置界面

#### 核心方法

**标签页服务 (TabService)**

```typescript
class TabService {
  // 基础操作
  async saveTabs(tabs: Tab[]): Promise<void> // 批量保存标签页
  async getTabs(): Promise<Tab[]> // 获取所有标签页
  async getTabById(id: string): Promise<Tab | undefined> // 根据ID获取标签页
  async createTab(tabData: Omit<Tab, 'lastModified'>): Promise<void> // 创建新标签页
  async updateTab(id: string, updates: Partial<Tab>): Promise<void> // 更新标签页
  async deleteTab(id: string): Promise<void> // 删除标签页

  // 状态管理
  async getActiveTab(): Promise<Tab | undefined> // 获取活跃标签页
  async setActiveTab(id: string): Promise<void> // 设置活跃标签页
  async getPinnedTabs(): Promise<Tab[]> // 获取固定标签页
  async getDirtyTabs(): Promise<Tab[]> // 获取未保存标签页
  async getTabsByType(type: string): Promise<Tab[]> // 根据类型获取标签页

  // 维护操作
  async cleanupOldTabs(daysToKeep: number): Promise<number> // 清理旧标签页
  async getTabStats(): Promise<TabStats> // 获取标签页统计信息
}
```

### 2. 窗口状态模块 (Window State Module)

#### 字段设计说明

| 字段名         | 类型    | 说明                      | 默认值    |
| -------------- | ------- | ------------------------- | --------- |
| `id`           | number  | 窗口ID，单窗口应用通常为1 | 1         |
| `isMaximized`  | boolean | 是否最大化状态            | false     |
| `isMinimized`  | boolean | 是否最小化状态            | false     |
| `isFullscreen` | boolean | 是否全屏状态              | false     |
| `isFocused`    | boolean | 是否获得焦点              | true      |
| `bounds`       | Bounds? | 窗口边界信息              | undefined |
| `updatedAt`    | string? | 最后更新时间              | undefined |

#### 窗口边界信息 (Bounds)

```typescript
interface Bounds {
  x: number // 窗口X坐标
  y: number // 窗口Y坐标
  width: number // 窗口宽度，最小值1
  height: number // 窗口高度，最小值1
}
```

#### 核心方法

```typescript
class WindowService {
  async saveWindowState(state: Omit<WindowState, 'id' | 'updatedAt'>): Promise<void>
  async getWindowState(): Promise<WindowState | undefined>
  async clearWindowState(): Promise<void>
  async initializeWindowState(): Promise<void>
}
```

### 3. 设置模块 (Settings Module)

#### 应用设置字段

| 字段名               | 类型      | 说明               | 默认值   | 约束   |
| -------------------- | --------- | ------------------ | -------- | ------ |
| `saveAppState`       | boolean   | 是否保存应用状态   | true     | -      |
| `restoreWindowState` | boolean   | 是否恢复窗口状态   | true     | -      |
| `restoreTabs`        | boolean   | 是否恢复标签页     | true     | -      |
| `maxRecentTabs`      | number    | 最大最近标签页数量 | 10       | 1-50   |
| `theme`              | ThemeType | 主题设置           | 'system' | 枚举值 |
| `language`           | string    | 语言设置           | 'zh-CN'  | -      |

#### 主题类型

```typescript
type ThemeType = 'light' | 'dark' | 'system'
```

#### 核心方法

```typescript
class SettingsService {
  // 基础操作
  async saveSetting(key: string, value: any): Promise<void>
  async getSetting(key: string, defaultValue?: any): Promise<any>
  async getAllSettings(): Promise<Record<string, any>>
  async deleteSetting(key: string): Promise<void>

  // 批量操作
  async saveSettings(settings: Record<string, any>): Promise<void>
  async deleteSettings(keys: string[]): Promise<void>

  // 默认设置
  async initializeDefaultSettings(): Promise<void>

  // 维护操作
  async cleanupEmptySettings(): Promise<number>
}
```

### 4. 最近文件模块 (Recent Files Module)

#### 字段设计说明

| 字段名         | 类型                 | 说明                     |
| -------------- | -------------------- | ------------------------ |
| `id`           | number?              | 自增主键                 |
| `path`         | string               | 文件完整路径，唯一约束   |
| `name`         | string               | 文件名                   |
| `lastAccessed` | string               | 最后访问时间 (ISO字符串) |
| `fileSize`     | number?              | 文件大小 (字节)          |
| `fileType`     | string?              | 文件类型/扩展名          |
| `metadata`     | Record<string, any>? | 文件元数据               |

## 数据流架构

### 1. 数据层级

```
应用层 (React Components)
    ↕ (IPC通信)
预加载层 (Preload Script)
    ↕ (IPC通信)
主进程层 (Main Process)
    ↕ (直接调用)
服务层 (Service Layer)
    ↕ (直接调用)
仓库层 (Repository Layer)
    ↕ (SQL操作)
数据库层 (SQLite Database)
```

### 2. 类型转换流程

```
前端类型 (Tab, WindowState, AppSettings)
    ↕ (JSON序列化/反序列化)
IPC传输 (JSON格式)
    ↕ (类型验证)
服务层类型 (相同的统一类型)
    ↕ (内部转换)
数据库类型 (DbTab, DbWindowState, DbSettings)
    ↕ (SQL操作)
SQLite存储 (原始数据格式)
```

### 3. 错误处理

所有异步操作都返回 `ApiResponse<T>` 格式：

```typescript
interface ApiResponse<T = any> {
  success: boolean // 操作是否成功
  data?: T // 响应数据
  error?: string // 错误信息
  timestamp: number // 响应时间戳
}
```

## 主进程架构 (`src/main/index.ts`)

### 应用生命周期管理

```typescript
// 应用启动流程
app.whenReady().then(async () => {
  // 1. 初始化数据库
  await initializeDatabase()

  // 2. 创建主窗口
  createWindow()

  // 3. 设置IPC通信
  setupDatabaseIPC()
  setupWindowControlsIPC(mainWindow)

  // 4. 恢复窗口状态
  await restoreWindowState()
})

// 窗口管理
function createWindow(): void {
  const { width, height } = screen.getPrimaryDisplay().workAreaSize

  mainWindow = new BrowserWindow({
    width: Math.min(width * 0.8, 1400),
    height: Math.min(height * 0.8, 900),
    minWidth: MIN_WIDTH,
    minHeight: MIN_HEIGHT,
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'hidden',
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false
    }
  })
}
```

### 窗口状态持久化

```typescript
// 保存窗口状态
const saveWindowState = async () => {
  try {
    const bounds = mainWindow.getBounds()
    await windowModule.save({
      isMaximized: mainWindow.isMaximized(),
      isMinimized: mainWindow.isMinimized(),
      isFullscreen: mainWindow.isFullScreen(),
      isFocused: mainWindow.isFocused(),
      bounds: bounds // 直接传递对象，不需要 JSON.stringify
    })
  } catch (error) {
    console.error('Failed to save window state:', error)
  }
}

// 恢复窗口状态
const restoreWindowState = async () => {
  try {
    const savedState = await windowModule.get()
    if (savedState?.bounds) {
      // bounds 现在已经是对象类型，不需要 JSON.parse
      savedBounds = {
        x: savedState.bounds.x || 0,
        y: savedState.bounds.y || 0,
        width: Math.max(savedState.bounds.width || DEFAULT_WIDTH, MIN_WIDTH),
        height: Math.max(savedState.bounds.height || DEFAULT_HEIGHT, MIN_HEIGHT)
      }
    }
  } catch (error) {
    console.error('Failed to restore window state:', error)
  }
}
```

## 数据库迁移系统

### 迁移结构

```typescript
interface Migration {
  version: number
  name: string
  up: (db: Database.Database) => void
}

const migrations: Migration[] = [
  {
    version: 1,
    name: 'initial_schema',
    up: (db) => {
      // 创建所有表结构
      // 创建索引
      // 初始化数据
    }
  }
]
```

### 迁移执行

```typescript
export async function runMigrations(db: Database.Database): Promise<void> {
  // 获取当前版本
  const currentVersion = getCurrentVersion(db)

  // 执行待处理的迁移
  for (const migration of migrations) {
    if (migration.version > currentVersion) {
      console.log(`Running migration: ${migration.name}`)
      migration.up(db)
      updateVersion(db, migration.version)
    }
  }
}
```

## 性能优化策略

### 1. 数据库优化

- **WAL模式**: 提高并发读写性能
- **缓存设置**: 64MB内存缓存
- **索引优化**: 为常用查询字段创建索引
- **批量操作**: 使用事务进行批量插入/更新

### 2. 内存管理

- **单例模式**: 数据库连接和服务实例
- **延迟加载**: 按需加载模块和数据
- **定期清理**: 自动清理过期数据

### 3. IPC优化

- **类型验证**: 使用Zod进行数据验证
- **错误处理**: 统一的错误响应格式
- **异步操作**: 所有数据库操作都是异步的

## 开发指南

### 1. 添加新的数据模型

1. **定义类型**: 在 `src/shared/types.ts` 中添加 Zod Schema
2. **数据库迁移**: 在 `src/main/database/migrations.ts` 中添加表结构
3. **创建Repository**: 继承 `BaseRepository` 实现数据访问
4. **创建Service**: 实现业务逻辑
5. **模块导出**: 在 `src/main/database/modules/` 中创建模块
6. **IPC接口**: 在 `src/main/ipc/` 中添加处理函数
7. **预加载API**: 在 `src/preload/index.ts` 中暴露接口

### 2. 数据库查询最佳实践

```typescript
// ✅ 好的做法
class ExampleRepository extends BaseRepository {
  getItems(): Item[] {
    const stmt = this.db.prepare('SELECT * FROM items WHERE active = 1')
    const dbResults = stmt.all() as DbItem[]
    return dbResults.map(this.convertDbItemToItem)
  }

  private convertDbItemToItem(dbItem: DbItem): Item {
    return {
      id: dbItem.id,
      active: Boolean(dbItem.active),
      metadata: dbItem.metadata ? JSON.parse(dbItem.metadata) : undefined
    }
  }
}

// ❌ 避免的做法
class BadRepository {
  getItems(): any[] {
    return this.db.prepare('SELECT * FROM items').all() // 没有类型转换
  }
}
```

### 3. 类型安全指南

```typescript
// ✅ 使用Zod验证
const validateInput = (data: unknown): Tab => {
  return TabSchema.parse(data) // 运行时验证
}

// ✅ 类型推断
type Tab = z.infer<typeof TabSchema> // 编译时类型

// ❌ 手动类型定义
interface ManualTab {
  id: string
  title: string
  // 容易与Schema不同步
}
```

## 构建和部署

### 开发环境

```bash
npm run dev          # 启动开发服务器 (热重载)
npm run typecheck    # TypeScript类型检查
npm run lint         # ESLint代码检查
npm run format       # Prettier代码格式化
```

### 生产构建

```bash
npm run build        # 构建所有平台
npm run build:win    # 构建Windows版本
npm run build:mac    # 构建macOS版本
npm run build:linux  # 构建Linux版本
```

### 配置文件说明

- **electron-builder.yml**: 应用打包配置
- **tsconfig.json**: TypeScript编译配置
- **tsconfig.node.json**: Node.js环境配置
- **tsconfig.web.json**: Web环境配置
- **components.json**: UI组件库配置

## 项目特色

### 1. 类型安全

- 使用Zod进行运行时类型验证
- TypeScript提供编译时类型检查
- 统一的类型定义避免不一致

### 2. 模块化架构

- 清晰的分层架构
- 单一职责原则
- 易于扩展和维护

### 3. 数据持久化

- SQLite本地数据库
- 完整的迁移系统
- 事务支持和性能优化

### 4. 现代化工具链

- Electron + React + TypeScript
- Vite构建工具
- Tailwind CSS样式
- Radix UI组件库

这份文档涵盖了Tbaw项目的完整代码架构、设计理念和实现细节，可以帮助您深入理解项目的各个方面。
