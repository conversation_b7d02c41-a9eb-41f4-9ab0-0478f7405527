/* 设计令牌 - 现代化标题栏设计系统 */

:root {
  /* 标题栏尺寸 */
  --titlebar-height: 40px;
  --titlebar-height-macos: 28px;
  --tab-border-radius: 8px;
  --tab-max-width: 240px;
  --tab-min-width: 120px;
  
  /* 颜色系统 - 浅色主题 */
  --titlebar-bg: #f8f9fa;
  --titlebar-border: #e9ecef;
  --titlebar-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  /* 标签页颜色 */
  --tab-active-bg: #ffffff;
  --tab-active-border: #dee2e6;
  --tab-active-text: #212529;
  --tab-active-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  --tab-inactive-bg: rgba(255, 255, 255, 0.6);
  --tab-inactive-border: rgba(222, 226, 230, 0.6);
  --tab-inactive-text: #6c757d;
  
  --tab-hover-bg: rgba(255, 255, 255, 0.8);
  --tab-hover-border: rgba(222, 226, 230, 0.8);
  --tab-hover-text: #495057;
  
  /* 按钮颜色 */
  --button-hover-bg: rgba(0, 0, 0, 0.05);
  --button-active-bg: rgba(0, 0, 0, 0.1);
  --button-text: #6c757d;
  --button-text-hover: #495057;
  
  /* 关闭按钮 */
  --close-button-hover-bg: #dc3545;
  --close-button-hover-text: #ffffff;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 深色主题 */
[data-theme="dark"] {
  --titlebar-bg: #2d3748;
  --titlebar-border: #4a5568;
  --titlebar-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  
  --tab-active-bg: #4a5568;
  --tab-active-border: #718096;
  --tab-active-text: #f7fafc;
  --tab-active-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  
  --tab-inactive-bg: rgba(74, 85, 104, 0.6);
  --tab-inactive-border: rgba(113, 128, 150, 0.6);
  --tab-inactive-text: #a0aec0;
  
  --tab-hover-bg: rgba(74, 85, 104, 0.8);
  --tab-hover-border: rgba(113, 128, 150, 0.8);
  --tab-hover-text: #e2e8f0;
  
  --button-hover-bg: rgba(255, 255, 255, 0.1);
  --button-active-bg: rgba(255, 255, 255, 0.2);
  --button-text: #a0aec0;
  --button-text-hover: #e2e8f0;
  
  --close-button-hover-bg: #e53e3e;
  --close-button-hover-text: #ffffff;
}

/* 高对比度主题 */
[data-theme="high-contrast"] {
  --titlebar-bg: #000000;
  --titlebar-border: #ffffff;
  
  --tab-active-bg: #ffffff;
  --tab-active-border: #000000;
  --tab-active-text: #000000;
  
  --tab-inactive-bg: #808080;
  --tab-inactive-border: #ffffff;
  --tab-inactive-text: #ffffff;
  
  --button-text: #ffffff;
  --button-hover-bg: #ffffff;
  --button-text-hover: #000000;
}

/* 动画关键帧 */
@keyframes tab-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes tab-slide-out {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
}

@keyframes button-press {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* 工具类 */
.animate-tab-in {
  animation: tab-slide-in var(--transition-normal);
}

.animate-tab-out {
  animation: tab-slide-out var(--transition-normal);
}

.animate-button-press {
  animation: button-press var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --tab-max-width: 180px;
    --tab-min-width: 100px;
  }
}

@media (max-width: 480px) {
  :root {
    --tab-max-width: 120px;
    --tab-min-width: 80px;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0s;
    --transition-normal: 0s;
    --transition-slow: 0s;
  }
  
  .animate-tab-in,
  .animate-tab-out,
  .animate-button-press {
    animation: none;
  }
}
