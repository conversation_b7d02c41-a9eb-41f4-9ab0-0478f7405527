import Database from 'better-sqlite3'
import { app } from 'electron'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'
import { runMigrations } from './migrations'
import type { DatabaseTab, DatabaseWindowState } from '../../shared/types'

// 导入仓库层
import { TabRepository } from './repositories/tab-repository'
import { SettingsRepository } from './repositories/settings-repository'
import { WindowRepository } from './repositories/window-repository'

// 导入服务层
import { TabService } from './services/tab-service'
import { SettingsService } from './services/settings-service'
import { WindowService } from './services/window-service'

class DatabaseManager {
  private static instance: DatabaseManager
  private db: Database.Database | null = null

  // 仓库实例
  private tabRepository: TabRepository | null = null
  private settingsRepository: SettingsRepository | null = null
  private windowRepository: WindowRepository | null = null

  // 服务实例
  private tabService: TabService | null = null
  private settingsService: SettingsService | null = null
  private windowService: WindowService | null = null

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  async initialize(): Promise<void> {
    try {
      // 确保数据目录存在
      const userDataPath = app.getPath('userData')
      const dbDir = join(userDataPath, 'database')

      if (!existsSync(dbDir)) {
        mkdirSync(dbDir, { recursive: true })
      }

      const dbPath = join(dbDir, 'tbaw.db')

      // 创建数据库连接
      this.db = new Database(dbPath, {
        // verbose: process.env.NODE_ENV === 'development' ? console.log : undefined
      })

      // 启用外键约束
      this.db.pragma('foreign_keys = ON')

      // 设置WAL模式以提高性能
      this.db.pragma('journal_mode = WAL')

      // 设置同步模式
      this.db.pragma('synchronous = NORMAL')

      // 设置缓存大小（单位：页，默认 4KB/页）
      this.db.pragma('cache_size = -64000') // 64MB 缓存

      // 运行数据库迁移
      await runMigrations(this.db)

      // 初始化仓库层
      this.tabRepository = new TabRepository(this.db)
      this.settingsRepository = new SettingsRepository(this.db)
      this.windowRepository = new WindowRepository(this.db)

      // 初始化服务层
      this.tabService = new TabService(this.tabRepository)
      this.settingsService = new SettingsService(this.settingsRepository)
      this.windowService = new WindowService(this.windowRepository)

      console.log('Database initialized successfully:', dbPath)
    } catch (error) {
      console.error('Database initialization failed:', error)
      throw error
    }
  }

  getDatabase(): Database.Database {
    if (!this.db) {
      throw new Error('Database not initialized')
    }
    return this.db
  }

  // 标签页相关操作
  async saveTabs(tabs: DatabaseTab[]): Promise<void> {
    if (!this.tabService) throw new Error('Database not initialized')
    return await this.tabService.saveTabs(tabs)
  }

  async getTabs(): Promise<DatabaseTab[]> {
    if (!this.tabService) throw new Error('Database not initialized')
    return await this.tabService.getTabs()
  }

  async getTabById(id: string): Promise<DatabaseTab | undefined> {
    if (!this.tabService) throw new Error('Database not initialized')
    return await this.tabService.getTabById(id)
  }

  async updateTab(id: string, updates: Partial<DatabaseTab>): Promise<void> {
    if (!this.tabService) throw new Error('Database not initialized')
    return await this.tabService.updateTab(id, updates)
  }

  async deleteTab(id: string): Promise<void> {
    if (!this.tabService) throw new Error('Database not initialized')
    return await this.tabService.deleteTab(id)
  }

  // 设置相关操作
  async saveSetting(key: string, value: any): Promise<void> {
    if (!this.settingsService) throw new Error('Database not initialized')
    return await this.settingsService.saveSetting(key, value)
  }

  async getSetting<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    if (!this.settingsService) throw new Error('Database not initialized')
    return await this.settingsService.getSetting(key, defaultValue)
  }

  async getAllSettings(): Promise<Record<string, any>> {
    if (!this.settingsService) throw new Error('Database not initialized')
    return await this.settingsService.getAllSettings()
  }

  async deleteSetting(key: string): Promise<void> {
    if (!this.settingsService) throw new Error('Database not initialized')
    return await this.settingsService.deleteSetting(key)
  }

  async saveSettings(settings: Record<string, any>): Promise<void> {
    if (!this.settingsService) throw new Error('Database not initialized')
    return await this.settingsService.saveSettings(settings)
  }

  // 窗口状态相关操作
  async saveWindowState(state: Omit<DatabaseWindowState, 'id' | 'updatedAt'>): Promise<void> {
    if (!this.windowService) throw new Error('Database not initialized')
    return await this.windowService.saveWindowState(state)
  }

  async getWindowState(): Promise<DatabaseWindowState | undefined> {
    if (!this.windowService) throw new Error('Database not initialized')
    return await this.windowService.getWindowState()
  }

  async updateWindowBounds(bounds: {
    x: number
    y: number
    width: number
    height: number
  }): Promise<void> {
    if (!this.windowService) throw new Error('Database not initialized')
    return await this.windowService.updateWindowBounds(bounds)
  }

  async updateMaximizedState(isMaximized: boolean): Promise<void> {
    if (!this.windowService) throw new Error('Database not initialized')
    return await this.windowService.updateMaximizedState(isMaximized)
  }

  // 清理操作
  async cleanup(): Promise<void> {
    if (!this.db || !this.tabService) return

    try {
      // 清理旧的标签页记录（保留最近30天）
      await this.tabService.cleanupOldTabs(30)

      // 优化数据库
      this.db.pragma('optimize')

      console.log('Database cleanup completed')
    } catch (error) {
      console.error('Database cleanup failed:', error)
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null

      // 清理服务和仓库实例
      this.tabService = null
      this.settingsService = null
      this.windowService = null
      this.tabRepository = null
      this.settingsRepository = null
      this.windowRepository = null

      console.log('Database connection closed')
    }
  }

  // 获取服务实例的方法（用于高级操作）
  getTabService(): TabService {
    if (!this.tabService) throw new Error('Database not initialized')
    return this.tabService
  }

  getSettingsService(): SettingsService {
    if (!this.settingsService) throw new Error('Database not initialized')
    return this.settingsService
  }

  getWindowService(): WindowService {
    if (!this.windowService) throw new Error('Database not initialized')
    return this.windowService
  }
}

export default DatabaseManager
