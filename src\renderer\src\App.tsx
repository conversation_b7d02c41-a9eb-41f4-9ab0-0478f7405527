import React, { useEffect } from 'react'
import { CustomTitlebar } from '@/components/titlebar/CustomTitlebar'
import { useTitlebarStore } from '@/stores/titlebar-store'
import { useAppState } from '@/hooks/useAppState'
import { useSettings } from '@/hooks/useSettings'
import '@/styles/globals.css'
import '@/styles/design-tokens.css'

function App(): React.JSX.Element {
  const { addTab, loadTabs, tabs } = useTitlebarStore()
  const { restoreAppState } = useAppState()
  const { getSetting } = useSettings()

  // 初始化应用数据
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // 恢复应用状态
        await restoreAppState()

        // 从数据库加载标签页（如果启用了标签页恢复）
        if (getSetting('saveAppState') && getSetting('restoreTabs')) {
          const response = await window.api.database.getTabs()
          if (response.success && response.data && response.data.length > 0) {
            loadTabs(response.data)
            return // 如果成功加载了标签页，就不需要创建默认标签页
          }
        }

        // 如果没有标签页或未启用恢复，创建默认的首页标签页
        if (tabs.length === 0) {
          addTab({
            id: 'home',
            title: '首页',
            type: 'home',
            isDirty: false,
            isPinned: true,
            closable: false,
            icon: undefined
          })
        }
      } catch (error) {
        console.error('Failed to initialize app:', error)

        // 如果出错，至少创建首页标签页
        if (tabs.length === 0) {
          addTab({
            id: 'home',
            title: '首页',
            type: 'home',
            isDirty: false,
            isPinned: true,
            closable: false,
            icon: undefined
          })
        }
      }
    }

    initializeApp()
  }, [addTab, loadTabs, tabs.length, restoreAppState, getSetting])

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <CustomTitlebar />

      {/* 主内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">欢迎使用作家助手</h1>
            <p className="text-gray-600">自定义标题栏和标签页管理系统已就绪</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
