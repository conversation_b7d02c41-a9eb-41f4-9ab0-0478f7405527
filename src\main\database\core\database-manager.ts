import Database from 'better-sqlite3'
import { app } from 'electron'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'
import { runMigrations } from '../migrations'

/**
 * 核心数据库管理器
 * 只负责数据库连接、初始化和基础管理
 */
class CoreDatabaseManager {
  private static instance: CoreDatabaseManager
  private db: Database.Database | null = null
  private isInitialized = false

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  static getInstance(): CoreDatabaseManager {
    if (!CoreDatabaseManager.instance) {
      CoreDatabaseManager.instance = new CoreDatabaseManager()
    }
    return CoreDatabaseManager.instance
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      // 确保数据目录存在
      const userDataPath = app.getPath('userData')
      const dbDir = join(userDataPath, 'database')

      if (!existsSync(dbDir)) {
        mkdirSync(dbDir, { recursive: true })
      }

      const dbPath = join(dbDir, 'tbaw.db')

      // 创建数据库连接
      this.db = new Database(dbPath, {
        verbose: process.env.NODE_ENV === 'development' ? console.log : undefined
      })

      // 启用外键约束
      this.db.pragma('foreign_keys = ON')

      // 设置WAL模式以提高性能
      this.db.pragma('journal_mode = WAL')

      // 设置同步模式
      this.db.pragma('synchronous = NORMAL')

      // 设置缓存大小（单位：页，默认 4KB/页）
      this.db.pragma('cache_size = -64000') // 64MB 缓存

      // 运行数据库迁移
      await runMigrations(this.db)

      this.isInitialized = true
      console.log('Database initialized successfully:', dbPath)
    } catch (error) {
      console.error('Database initialization failed:', error)
      throw error
    }
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): Database.Database {
    if (!this.db || !this.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.db
  }

  /**
   * 检查数据库是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized && this.db !== null
  }

  /**
   * 执行数据库优化
   */
  async optimize(): Promise<void> {
    if (!this.db) return

    try {
      this.db.pragma('optimize')
      console.log('Database optimization completed')
    } catch (error) {
      console.error('Database optimization failed:', error)
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null
      this.isInitialized = false
      console.log('Database connection closed')
    }
  }

  /**
   * 获取数据库统计信息
   */
  getStats(): {
    isInitialized: boolean
    hasConnection: boolean
    dbPath?: string
  } {
    return {
      isInitialized: this.isInitialized,
      hasConnection: this.db !== null,
      dbPath: this.db ? this.db.name : undefined
    }
  }
}

export default CoreDatabaseManager
