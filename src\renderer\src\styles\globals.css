@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* 标题栏设计令牌 */
  --titlebar-height: 40px;
  --titlebar-height-macos: 28px;
  --tab-border-radius: 8px;
  --tab-max-width: 240px;
  --tab-min-width: 120px;

  /* 标题栏颜色 */
  --titlebar-bg: #f8f9fa;
  --titlebar-border: #e9ecef;
  --titlebar-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  /* 标签页颜色 */
  --tab-active-bg: #ffffff;
  --tab-active-border: #dee2e6;
  --tab-active-text: #212529;
  --tab-active-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  --tab-inactive-bg: rgba(255, 255, 255, 0.6);
  --tab-inactive-border: rgba(222, 226, 230, 0.6);
  --tab-inactive-text: #6c757d;

  --tab-hover-bg: rgba(255, 255, 255, 0.8);
  --tab-hover-border: rgba(222, 226, 230, 0.8);
  --tab-hover-text: #495057;

  /* 按钮颜色 */
  --button-hover-bg: rgba(0, 0, 0, 0.05);
  --button-active-bg: rgba(0, 0, 0, 0.1);
  --button-text: #6c757d;
  --button-text-hover: #495057;

  /* 关闭按钮 */
  --close-button-hover-bg: #dc3545;
  --close-button-hover-text: #ffffff;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.15);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* 禁用文本选择 */
  .select-none {
    user-select: none;
  }

  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 2px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }
}

@layer components {
  /* 标题栏特定样式 */
  .titlebar-drag-region {
    -webkit-app-region: drag;
  }

  .titlebar-no-drag {
    -webkit-app-region: no-drag;
  }

  /* 标签页过渡动画 */
  .tab-transition {
    transition: all var(--transition-fast);

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
  }

  /* 窗口控制按钮样式 */
  .window-control-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2rem;
    width: 2rem;
    transition: background-color var(--transition-fast);

    &:hover {
      background-color: var(--button-hover-bg);
    }

    &.close:hover {
      background-color: var(--close-button-hover-bg);
      color: var(--close-button-hover-text);
    }
  }

  /* 平台特定样式 */
  .platform-darwin .titlebar {
    height: var(--titlebar-height-macos);
    padding-left: 78px;
  }

  .platform-win32 .titlebar,
  .platform-linux .titlebar {
    height: var(--titlebar-height);
  }
}

/* 动画关键帧 */
@keyframes tab-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes tab-slide-out {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
}

@keyframes button-press {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* 工具类 */
.animate-tab-in {
  animation: tab-slide-in var(--transition-normal);
}

.animate-tab-out {
  animation: tab-slide-out var(--transition-normal);
}

.animate-button-press {
  animation: button-press var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
  @theme inline {
    --tab-max-width: 180px;
    --tab-min-width: 100px;
  }
}

@media (max-width: 480px) {
  @theme inline {
    --tab-max-width: 120px;
    --tab-min-width: 80px;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  @theme inline {
    --transition-fast: 0s;
    --transition-normal: 0s;
    --transition-slow: 0s;
  }

  .animate-tab-in,
  .animate-tab-out,
  .animate-button-press {
    animation: none;
  }
}
