import { ipc<PERSON>ain } from 'electron'
import { tabs, settings, window as windowModule } from '../database'
import type { ApiResponse, Tab, AppSettings, WindowState } from '../../shared/types'
import { createApiResponse } from '../../shared/types'

export function setupDatabaseIPC(): void {
  // 保存标签页
  ipcMain.handle('db:save-tabs', async (_, tabsData: Tab[]): Promise<ApiResponse> => {
    try {
      // 转换为数据库格式（新的服务层期望布尔值）
      const dbTabs = tabsData.map((tab) => ({
        id: tab.id,
        title: tab.title,
        active: tab.active,
        isDirty: tab.isDirty,
        isPinned: tab.isPinned,
        icon: tab.icon,
        closable: tab.closable,
        tooltip: tab.tooltip,
        lastModified: tab.lastModified?.toISOString(),
        metadata: tab.metadata ? JSON.stringify(tab.metadata) : undefined,
        path: tab.path,
        type: tab.type,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))

      await tabs.save(dbTabs)
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to save tabs:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 获取标签页
  ipcMain.handle('db:get-tabs', async (): Promise<ApiResponse<Tab[]>> => {
    try {
      const dbTabs = await tabs.getAll()

      // 转换为应用格式
      const tabsData: Tab[] = dbTabs.map((dbTab) => ({
        id: dbTab.id,
        title: dbTab.title,
        active: Boolean(dbTab.active),
        isDirty: Boolean(dbTab.isDirty),
        isPinned: Boolean(dbTab.isPinned),
        icon: dbTab.icon,
        closable: Boolean(dbTab.closable),
        tooltip: dbTab.tooltip,
        lastModified: dbTab.lastModified ? new Date(dbTab.lastModified) : undefined,
        metadata: dbTab.metadata ? JSON.parse(dbTab.metadata) : undefined,
        path: dbTab.path,
        type: dbTab.type as 'home' | 'folder' | 'file'
      }))

      return createApiResponse(true, tabsData)
    } catch (error) {
      console.error('Failed to get tabs:', error)
      return createApiResponse(
        false,
        [] as Tab[],
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 保存设置
  ipcMain.handle('db:save-setting', async (_, key: string, value: any): Promise<ApiResponse> => {
    try {
      await settings.save(key, value)
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to save setting:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 获取设置
  ipcMain.handle(
    'db:get-setting',
    async (_, key: string, defaultValue?: any): Promise<ApiResponse<any>> => {
      try {
        const value = await settings.get(key, defaultValue)
        return createApiResponse(true, value)
      } catch (error) {
        console.error('Failed to get setting:', error)
        return createApiResponse(
          false,
          undefined,
          error instanceof Error ? error.message : 'Unknown error'
        )
      }
    }
  )

  // 获取所有设置
  ipcMain.handle('db:get-all-settings', async (): Promise<ApiResponse<AppSettings>> => {
    try {
      const settingsData = await settings.getAll()
      return createApiResponse(true, settingsData as AppSettings)
    } catch (error) {
      console.error('Failed to get all settings:', error)
      return createApiResponse(
        false,
        {} as AppSettings,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 保存窗口状态
  ipcMain.handle('db:save-window-state', async (_, state: WindowState): Promise<ApiResponse> => {
    try {
      const dbState = {
        isMaximized: state.isMaximized,
        isMinimized: state.isMinimized,
        isFullscreen: state.isFullscreen,
        isFocused: state.isFocused,
        bounds: state.bounds ? JSON.stringify(state.bounds) : undefined
      }

      await windowModule.save(dbState)
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to save window state:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 获取窗口状态
  ipcMain.handle('db:get-window-state', async (): Promise<ApiResponse<WindowState | null>> => {
    try {
      const dbState = await windowModule.get()

      if (!dbState) {
        return createApiResponse(true, null)
      }

      const state: WindowState = {
        isMaximized: Boolean(dbState.isMaximized),
        isMinimized: Boolean(dbState.isMinimized),
        isFullscreen: Boolean(dbState.isFullscreen),
        isFocused: Boolean(dbState.isFocused),
        bounds: dbState.bounds ? JSON.parse(dbState.bounds) : undefined
      }

      return createApiResponse(true, state)
    } catch (error) {
      console.error('Failed to get window state:', error)
      return createApiResponse(
        false,
        null,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })

  // 数据库清理
  ipcMain.handle('db:cleanup', async (): Promise<ApiResponse> => {
    try {
      await tabs.cleanup(30)
      await settings.cleanup()
      return createApiResponse(true)
    } catch (error) {
      console.error('Failed to cleanup database:', error)
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : 'Unknown error'
      )
    }
  })
}
