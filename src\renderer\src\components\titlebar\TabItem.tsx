import React, { useCallback, useState } from 'react'
import { X, Pin, Home, Folder, File } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Tab, TitlebarConfig } from '@/types'

interface TabItemProps {
  tab: Tab
  onAction: (action: string, tabId: string, payload?: any) => void
  config: TitlebarConfig
  width?: number
}

export function TabItem({ tab, onAction, width = 200 }: TabItemProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isClosing, setIsClosing] = useState(false)

  // 事件处理
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      onAction('activate', tab.id)
    },
    [onAction, tab.id]
  )

  const handleClose = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()
      setIsClosing(true)
      // 添加关闭动画延迟
      setTimeout(() => {
        onAction('close', tab.id)
      }, 150)
    },
    [onAction, tab.id]
  )

  const handleMiddleClick = useCallback(
    (e: React.MouseEvent) => {
      if (e.button === 1) {
        // 中键
        e.preventDefault()
        if (tab.closable && tab.type !== 'home') {
          onAction('close', tab.id)
        }
      }
    },
    [onAction, tab.id, tab.closable, tab.type]
  )

  const handlePin = useCallback(() => {
    onAction(tab.isPinned ? 'unpin' : 'pin', tab.id)
  }, [onAction, tab.id, tab.isPinned])

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    // 这里可以添加右键菜单逻辑
  }, [])

  // 获取图标
  const getIcon = () => {
    if (tab.icon) {
      return <img src={tab.icon} alt="" className="w-4 h-4 shrink-0" />
    }

    switch (tab.type) {
      case 'home':
        return <Home className="w-4 h-4 shrink-0" />
      case 'folder':
        return <Folder className="w-4 h-4 shrink-0" />
      case 'file':
      default:
        return <File className="w-4 h-4 shrink-0" />
    }
  }

  // 计算标签页样式类
  const getTabClasses = () => {
    const baseClasses = [
      'relative flex items-center gap-2 px-3 cursor-pointer group select-none',
      'transition-all duration-[var(--transition-normal)]',
      isClosing && 'animate-tab-out'
    ]

    if (tab.active) {
      baseClasses.push('tab-active')
    } else if (isHovered) {
      baseClasses.push(
        'bg-[var(--tab-hover-bg)] text-[var(--tab-hover-text)]',
        'border border-[var(--tab-hover-border)]',
        'rounded-[var(--tab-inactive-radius)]',
        'h-[calc(100%-4px)]',
        'mt-[2px]',
        'mb-[2px]',
        'mr-[2px]'
      )
    } else {
      baseClasses.push('tab-inactive')
    }

    if (tab.isPinned) {
      baseClasses.push('min-w-[44px] max-w-[44px]')
    } else {
      baseClasses.push('min-w-[120px] max-w-[240px]')
    }

    return cn(...baseClasses)
  }

  return (
    <div
      className={getTabClasses()}
      style={
        {
          width: tab.isPinned ? 44 : Math.min(Math.max(width, 120), 240),
          WebkitAppRegion: 'no-drag'
        } as React.CSSProperties
      }
      onClick={handleClick}
      onMouseDown={handleMiddleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onContextMenu={handleContextMenu}
      title={tab.tooltip || tab.title}
    >
      {/* 图标 */}
      <div className="shrink-0 flex items-center">{getIcon()}</div>

      {/* 固定图标 */}
      {tab.isPinned && <Pin className="w-3 h-3 text-blue-500 shrink-0 absolute top-1 right-1" />}

      {/* 标题 */}
      {!tab.isPinned && (
        <span
          className={cn(
            'flex-1 truncate text-sm font-medium',
            'transition-colors duration-[var(--transition-fast)]'
          )}
        >
          {tab.title}
        </span>
      )}

      {/* 未保存指示器 */}
      {tab.isDirty && !tab.isPinned && (
        <div className="w-2 h-2 bg-orange-400 rounded-full shrink-0 animate-pulse" />
      )}

      {/* 关闭按钮 */}
      {tab.closable && tab.type !== 'home' && !tab.isPinned && (isHovered || tab.active) && (
        <button
          onClick={handleClose}
          className={cn(
            'h-5 w-5 p-0 shrink-0 rounded-full',
            'transition-all duration-[var(--transition-fast)]',
            'flex items-center justify-center',
            'hover:bg-[var(--close-button-hover-bg)] hover:text-[var(--close-button-hover-text)]',
            'text-[var(--button-text)] hover:text-[var(--button-text-hover)]',
            'opacity-0 group-hover:opacity-100',
            tab.active && 'opacity-70'
          )}
          aria-label={`关闭 ${tab.title}`}
        >
          <X className="h-3 w-3" />
        </button>
      )}

      {/* 固定/取消固定按钮（仅在悬停时显示，且不是首页） */}
      {tab.type !== 'home' && isHovered && !tab.closable && (
        <button
          onClick={handlePin}
          className={cn(
            'h-5 w-5 p-0 shrink-0 opacity-70 hover:opacity-100',
            'hover:bg-gray-200 rounded',
            'transition-opacity duration-150',
            'flex items-center justify-center'
          )}
          aria-label={tab.isPinned ? '取消固定' : '固定标签页'}
        >
          <Pin className={cn('h-3 w-3', tab.isPinned ? 'text-blue-500' : 'text-gray-400')} />
        </button>
      )}
    </div>
  )
}
